from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any
from crm_project.database import get_session
from crm_project.crud import (
    create_task, get_tasks, get_task, update_task, complete_task, delete_task,
    get_overdue_tasks, get_tasks_due_today, get_task_summary
)
from crm_project.schemas import TaskCreate, TaskRead, TaskUpdate, TaskDetailRead

router = APIRouter(prefix="/tasks", tags=["Tasks"])

@router.post("/", response_model=TaskRead)
def add_task(payload: TaskCreate, db: Session = Depends(get_session)):
    """Create a new task"""
    return create_task(db, payload)

@router.get("/", response_model=list[TaskRead])
def list_tasks(
    status: Optional[str] = Query(None, description="Filter by status"),
    priority: Optional[str] = Query(None, description="Filter by priority"),
    task_type: Optional[str] = Query(None, description="Filter by task type"),
    contact_id: Optional[int] = Query(None, description="Filter by contact"),
    lead_id: Optional[int] = Query(None, description="Filter by lead"),
    account_id: Optional[int] = Query(None, description="Filter by account"),
    db: Session = Depends(get_session)
):
    """List tasks with optional filtering"""
    return get_tasks(db, status, priority, task_type, contact_id, lead_id, account_id)

@router.get("/summary", response_model=Dict[str, Any])
def get_tasks_summary(db: Session = Depends(get_session)):
    """Get task summary statistics"""
    return get_task_summary(db)

@router.get("/overdue", response_model=list[TaskRead])
def list_overdue_tasks(db: Session = Depends(get_session)):
    """Get all overdue tasks"""
    return get_overdue_tasks(db)

@router.get("/due-today", response_model=list[TaskRead])
def list_tasks_due_today(db: Session = Depends(get_session)):
    """Get all tasks due today"""
    return get_tasks_due_today(db)

@router.get("/{task_id}", response_model=TaskDetailRead)
def get_task_detail(task_id: int, db: Session = Depends(get_session)):
    """Get task details"""
    task = get_task(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")

    # Add related entity names
    task_detail = TaskDetailRead.from_orm(task)
    if task.contact:
        task_detail.contact_name = f"{task.contact.first_name} {task.contact.last_name}"
    if task.lead:
        task_detail.lead_title = task.lead.title
    if task.account:
        task_detail.account_name = task.account.name

    return task_detail

@router.put("/{task_id}", response_model=TaskRead)
def update_task_endpoint(task_id: int, payload: TaskUpdate, db: Session = Depends(get_session)):
    """Update a task"""
    task = update_task(db, task_id, payload)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task

@router.put("/{task_id}/complete", response_model=TaskRead)
def complete_task_endpoint(task_id: int, db: Session = Depends(get_session)):
    """Mark a task as completed"""
    task = complete_task(db, task_id)
    if not task:
        raise HTTPException(status_code=404, detail="Task not found")
    return task

@router.delete("/{task_id}", response_model=bool)
def remove_task(task_id: int, db: Session = Depends(get_session)):
    """Delete a task"""
    return delete_task(db, task_id)
