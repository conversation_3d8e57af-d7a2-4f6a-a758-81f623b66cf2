from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, HttpUrl, field_validator

class ContactBase(BaseModel):
    first_name: str
    last_name: str
    email: str
    phone: Optional[str] = None
    job_title: Optional[str] = None
    department: Optional[str] = None
    account_id: Optional[int] = None
    tags: Optional[List[str]] = []
    custom_fields: Optional[Dict[str, Any]] = {}
    linkedin_url: Optional[str] = None
    twitter_handle: Optional[str] = None
    address: Optional[str] = None
    status: Optional[str] = "active"
    lead_source: Optional[str] = None

class ContactCreate(ContactBase):
    pass

class ContactUpdate(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    phone: Optional[str] = None
    job_title: Optional[str] = None
    department: Optional[str] = None
    account_id: Optional[int] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None
    linkedin_url: Optional[str] = None
    twitter_handle: Optional[str] = None
    address: Optional[str] = None
    status: Optional[str] = None
    lead_source: Optional[str] = None

class ContactRead(ContactBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    @field_validator('tags', mode='before')
    @classmethod
    def validate_tags(cls, v):
        return v if v is not None else []

    @field_validator('custom_fields', mode='before')
    @classmethod
    def validate_custom_fields(cls, v):
        return v if v is not None else {}

    @field_validator('status', mode='before')
    @classmethod
    def validate_status(cls, v):
        return v if v is not None else "active"

    class Config:
        from_attributes = True

# Contact Notes
class ContactNoteBase(BaseModel):
    content: str
    note_type: str = "general"
    created_by: Optional[str] = None

class ContactNoteCreate(ContactNoteBase):
    contact_id: int

class ContactNoteRead(ContactNoteBase):
    id: int
    contact_id: int
    created_at: datetime
    class Config:
        from_attributes = True

# Contact Activities
class ContactActivityRead(BaseModel):
    id: int
    contact_id: int
    activity_type: str
    description: str
    activity_data: Dict[str, Any]
    created_at: datetime
    class Config:
        from_attributes = True

# Enhanced Contact Read with related data
class ContactDetailRead(ContactRead):
    notes: List[ContactNoteRead] = []
    activities: List[ContactActivityRead] = []
    account_name: Optional[str] = None

class AccountBase(BaseModel):
    name: str
    industry: Optional[str]
    website: Optional[str]

class AccountCreate(AccountBase):
    pass

class AccountRead(AccountBase):
    id: int
    created_at: datetime
    class Config:
        from_attributes = True

class LeadBase(BaseModel):
    title: str
    description: Optional[str] = None
    contact_id: int
    account_id: Optional[int] = None
    stage: str = "prospect"  # prospect, qualified, proposal, negotiation, closed_won, closed_lost
    probability: int = 10  # 0-100%
    value: int = 0  # Value in cents
    expected_close_date: Optional[datetime] = None
    source: Optional[str] = None
    campaign: Optional[str] = None
    status: str = "active"  # active, inactive, archived
    priority: str = "medium"  # low, medium, high, urgent
    tags: Optional[List[str]] = []
    custom_fields: Optional[Dict[str, Any]] = {}

class LeadCreate(LeadBase):
    pass

class LeadUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    contact_id: Optional[int] = None
    account_id: Optional[int] = None
    stage: Optional[str] = None
    probability: Optional[int] = None
    value: Optional[int] = None
    expected_close_date: Optional[datetime] = None
    source: Optional[str] = None
    campaign: Optional[str] = None
    status: Optional[str] = None
    priority: Optional[str] = None
    tags: Optional[List[str]] = None
    custom_fields: Optional[Dict[str, Any]] = None

class LeadRead(LeadBase):
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None

    @field_validator('tags', mode='before')
    @classmethod
    def validate_tags(cls, v):
        return v if v is not None else []

    @field_validator('custom_fields', mode='before')
    @classmethod
    def validate_custom_fields(cls, v):
        return v if v is not None else {}

    class Config:
        from_attributes = True

# Lead Notes
class LeadNoteBase(BaseModel):
    content: str
    note_type: str = "general"
    created_by: Optional[str] = None

class LeadNoteCreate(LeadNoteBase):
    lead_id: int

class LeadNoteRead(LeadNoteBase):
    id: int
    lead_id: int
    created_at: datetime
    class Config:
        from_attributes = True

# Lead Activities
class LeadActivityRead(BaseModel):
    id: int
    lead_id: int
    activity_type: str
    description: str
    activity_data: Dict[str, Any]
    created_at: datetime
    class Config:
        from_attributes = True

# Enhanced Lead Read with related data
class LeadDetailRead(LeadRead):
    contact_name: Optional[str] = None
    account_name: Optional[str] = None
    notes: List[LeadNoteRead] = []
    activities: List[LeadActivityRead] = []

class TaskBase(BaseModel):
    title: str
    description: Optional[str] = None
    task_type: str = "general"  # call, email, meeting, follow_up, demo, proposal, etc.
    priority: str = "medium"  # low, medium, high, urgent
    status: str = "pending"  # pending, in_progress, completed, cancelled
    due_date: Optional[datetime] = None
    start_date: Optional[datetime] = None
    estimated_duration: Optional[int] = None  # Duration in minutes
    contact_id: Optional[int] = None
    lead_id: Optional[int] = None
    account_id: Optional[int] = None
    tags: Optional[List[str]] = []
    notes: Optional[str] = None

    @field_validator('tags', mode='before')
    @classmethod
    def validate_tags(cls, v):
        if v is None:
            return []
        return v

class TaskCreate(TaskBase):
    pass

class TaskUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    task_type: Optional[str] = None
    priority: Optional[str] = None
    status: Optional[str] = None
    due_date: Optional[datetime] = None
    start_date: Optional[datetime] = None
    estimated_duration: Optional[int] = None
    contact_id: Optional[int] = None
    lead_id: Optional[int] = None
    account_id: Optional[int] = None
    tags: Optional[List[str]] = None
    notes: Optional[str] = None
    completed: Optional[bool] = None

class TaskRead(TaskBase):
    id: int
    completed: bool
    completed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    @field_validator('tags', mode='before')
    @classmethod
    def validate_tags_read(cls, v):
        if v is None:
            return []
        if isinstance(v, str):
            try:
                import json
                return json.loads(v)
            except (json.JSONDecodeError, TypeError):
                return []
        if isinstance(v, list):
            return v
        return []

    class Config:
        from_attributes = True

# Enhanced Task Read with related data
class TaskDetailRead(TaskRead):
    contact_name: Optional[str] = None
    lead_title: Optional[str] = None
    account_name: Optional[str] = None

# Webhook Schemas
class WebhookBase(BaseModel):
    name: str
    url: HttpUrl
    events: List[str]
    secret: Optional[str] = None
    active: bool = True

class WebhookCreate(WebhookBase):
    pass

class WebhookRead(WebhookBase):
    id: int
    created_at: datetime
    last_triggered: Optional[datetime] = None
    class Config:
        from_attributes = True

class WebhookUpdate(BaseModel):
    name: Optional[str] = None
    url: Optional[HttpUrl] = None
    events: Optional[List[str]] = None
    secret: Optional[str] = None
    active: Optional[bool] = None

# Webhook Log Schemas
class WebhookLogRead(BaseModel):
    id: int
    webhook_id: int
    event_type: str
    payload: Dict[str, Any]
    response_status: Optional[int] = None
    response_body: Optional[str] = None
    created_at: datetime
    class Config:
        from_attributes = True

# Event Schemas
class WebhookEvent(BaseModel):
    event_type: str
    data: Dict[str, Any]
    timestamp: datetime
