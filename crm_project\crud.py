from sqlalchemy.orm import Session, joinedload
from sqlalchemy.exc import NoResultFound
from crm_project.models import Contact, Account, Lead, Task, Webhook, WebhookLog, ContactNote, ContactActivity, LeadNote, LeadActivity, Setting, User
from crm_project.schemas import (
    ContactCreate, ContactUpdate, ContactNoteCreate, AccountCreate,
    LeadCreate, LeadUpdate, LeadNoteCreate, TaskCreate, TaskUpdate, WebhookCreate, WebhookUpdate
)
from typing import List, Optional, Dict, Any
from datetime import datetime
import requests
import logging
from passlib.context import CryptContext

# Relay.app webhook URL - Set your Relay.app webhook URL here or via environment/config
RELAY_WEBHOOK_URL = None  

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Contacts

def get_setting(db: Session, key: str) -> Optional[str]:
    setting = db.query(Setting).filter(Setting.key == key).first()
    return setting.value if setting else None

def set_setting(db: Session, key: str, value: str) -> None:
    setting = db.query(Setting).filter(Setting.key == key).first()
    if setting:
        setting.value = value
    else:
        setting = Setting(key=key, value=value)
        db.add(setting)
    db.commit()

def send_relay_webhook(event: str, payload: dict, db: Session, webhook_url: str = None):
    """
    Send a POST request to Relay.app webhook URL with event and payload.
    Uses the value from the settings table if webhook_url is not provided.
    """
    url = webhook_url or get_setting(db, "relay_webhook_url")
    if not url:
        logging.warning("Relay.app webhook URL not set. Skipping webhook call.")
        return
    data = {"event": event, **payload}
    try:
        resp = requests.post(url, json=data, timeout=5)
        resp.raise_for_status()
        logging.info(f"Relay.app webhook sent: {event}")
    except Exception as e:
        logging.error(f"Relay.app webhook failed: {e}")

def create_contact(db: Session, data: ContactCreate) -> Contact:
    contact_data = data.dict()
    obj = Contact(**contact_data)
    db.add(obj)
    db.commit()
    db.refresh(obj)

    # Create activity record
    create_contact_activity(
        db, obj.id, "contact.created",
        f"Contact {obj.first_name} {obj.last_name} was created"
    )

    # Relay.app integration
    send_relay_webhook(
        event="contact.created",
        payload={
            "contact_id": obj.id,
            "first_name": obj.first_name,
            "last_name": obj.last_name,
            "email": obj.email,
            "phone": obj.phone,
            "created_at": obj.created_at.isoformat(),
        },
        db=db
    )

    return obj

def get_contacts(db: Session) -> List[Contact]:
    return db.query(Contact).options(joinedload(Contact.account)).all()

def get_contact(db: Session, contact_id: int) -> Optional[Contact]:
    return db.query(Contact).options(
        joinedload(Contact.account),
        joinedload(Contact.notes),
        joinedload(Contact.activities)
    ).get(contact_id)

def update_contact(db: Session, contact_id: int, data: ContactUpdate) -> Optional[Contact]:
    contact = db.query(Contact).get(contact_id)
    if not contact:
        return None

    update_data = data.dict(exclude_unset=True)
    old_values = {}

    for field, value in update_data.items():
        if hasattr(contact, field):
            old_values[field] = getattr(contact, field)
            setattr(contact, field, value)

    contact.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(contact)

    # Create activity record
    changes = [f"{k}: {old_values[k]} → {v}" for k, v in update_data.items()]
    create_contact_activity(
        db, contact.id, "contact.updated",
        f"Contact updated: {', '.join(changes)}"
    )

    return contact

def delete_contact(db: Session, contact_id: int) -> bool:
    contact = db.query(Contact).get(contact_id)
    if not contact:
        return False
    db.delete(contact)
    db.commit()
    return True

# Contact Notes

def create_contact_note(db: Session, data: ContactNoteCreate) -> ContactNote:
    obj = ContactNote(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)

    # Create activity record
    create_contact_activity(
        db, obj.contact_id, "note.created",
        f"Note added: {obj.content[:50]}{'...' if len(obj.content) > 50 else ''}"
    )

    return obj

def get_contact_notes(db: Session, contact_id: int) -> List[ContactNote]:
    return db.query(ContactNote).filter(ContactNote.contact_id == contact_id).order_by(ContactNote.created_at.desc()).all()

def delete_contact_note(db: Session, note_id: int) -> bool:
    note = db.query(ContactNote).get(note_id)
    if not note:
        return False

    contact_id = note.contact_id
    db.delete(note)
    db.commit()

    # Create activity record
    create_contact_activity(
        db, contact_id, "note.deleted",
        "Note was deleted"
    )

    return True

# Contact Activities

def create_contact_activity(db: Session, contact_id: int, activity_type: str, description: str, activity_data: dict = None) -> ContactActivity:
    obj = ContactActivity(
        contact_id=contact_id,
        activity_type=activity_type,
        description=description,
        activity_data=activity_data or {}
    )
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_contact_activities(db: Session, contact_id: int, limit: int = 50) -> List[ContactActivity]:
    return db.query(ContactActivity).filter(
        ContactActivity.contact_id == contact_id
    ).order_by(ContactActivity.created_at.desc()).limit(limit).all()

# Accounts

def create_account(db: Session, data: AccountCreate) -> Account:
    obj = Account(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_accounts(db: Session) -> List[Account]:
    return db.query(Account).all()

def get_account(db: Session, account_id: int) -> Optional[Account]:
    return db.query(Account).get(account_id)

# Leads

def create_lead(db: Session, data: LeadCreate) -> Lead:
    lead_data = data.dict()
    obj = Lead(**lead_data)
    db.add(obj)
    db.commit()
    db.refresh(obj)

    # Create activity record
    create_lead_activity(
        db, obj.id, "lead.created",
        f"Lead '{obj.title}' was created in {obj.stage} stage"
    )

    # Relay.app integration
    send_relay_webhook(
        event="lead.created",
        payload={
            "lead_id": obj.id,
            "title": obj.title,
            "stage": obj.stage,
            "probability": obj.probability,
            "value": obj.value,
            "created_at": obj.created_at.isoformat(),
        },
        db=db
    )

    return obj

def get_leads(db: Session) -> List[Lead]:
    return db.query(Lead).all()

def get_lead(db: Session, lead_id: int) -> Optional[Lead]:
    return db.query(Lead).get(lead_id)

def update_lead(db: Session, lead_id: int, data: LeadUpdate) -> Optional[Lead]:
    lead = db.query(Lead).get(lead_id)
    if not lead:
        return None

    update_data = data.dict(exclude_unset=True)
    old_values = {}

    for field, value in update_data.items():
        if hasattr(lead, field):
            old_values[field] = getattr(lead, field)
            setattr(lead, field, value)

    lead.updated_at = datetime.utcnow()

    # Handle stage changes
    if 'stage' in update_data:
        if update_data['stage'] in ['closed_won', 'closed_lost']:
            lead.closed_at = datetime.utcnow()
        elif old_values.get('stage') in ['closed_won', 'closed_lost']:
            lead.closed_at = None

    db.commit()
    db.refresh(lead)

    # Create activity record
    changes = [f"{k}: {old_values[k]} → {v}" for k, v in update_data.items()]
    create_lead_activity(
        db, lead.id, "lead.updated",
        f"Lead updated: {', '.join(changes)}"
    )

    return lead

def delete_lead(db: Session, lead_id: int) -> bool:
    lead = db.query(Lead).get(lead_id)
    if not lead:
        return False
    db.delete(lead)
    db.commit()
    return True

def get_leads_by_stage(db: Session, stage: str) -> List[Lead]:
    return db.query(Lead).filter(Lead.stage == stage).all()

def get_pipeline_summary(db: Session) -> Dict[str, Any]:
    """Get pipeline summary with counts and values by stage"""
    stages = ['prospect', 'qualified', 'proposal', 'negotiation', 'closed_won', 'closed_lost']
    summary = {}

    for stage in stages:
        leads = db.query(Lead).filter(Lead.stage == stage).all()
        summary[stage] = {
            'count': len(leads),
            'total_value': sum(lead.value for lead in leads),
            'avg_probability': sum(lead.probability for lead in leads) / len(leads) if leads else 0
        }

    return summary

# Lead Notes

def create_lead_note(db: Session, data: LeadNoteCreate) -> LeadNote:
    obj = LeadNote(**data.dict())
    db.add(obj)
    db.commit()
    db.refresh(obj)

    # Create activity record
    create_lead_activity(
        db, obj.lead_id, "note.created",
        f"Note added: {obj.content[:50]}{'...' if len(obj.content) > 50 else ''}"
    )

    return obj

def get_lead_notes(db: Session, lead_id: int) -> List[LeadNote]:
    return db.query(LeadNote).filter(LeadNote.lead_id == lead_id).order_by(LeadNote.created_at.desc()).all()

def delete_lead_note(db: Session, note_id: int) -> bool:
    note = db.query(LeadNote).get(note_id)
    if not note:
        return False

    lead_id = note.lead_id
    db.delete(note)
    db.commit()

    # Create activity record
    create_lead_activity(
        db, lead_id, "note.deleted",
        "Note was deleted"
    )

    return True

# Lead Activities

def create_lead_activity(db: Session, lead_id: int, activity_type: str, description: str, activity_data: dict = None) -> LeadActivity:
    obj = LeadActivity(
        lead_id=lead_id,
        activity_type=activity_type,
        description=description,
        activity_data=activity_data or {}
    )
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_lead_activities(db: Session, lead_id: int, limit: int = 50) -> List[LeadActivity]:
    return db.query(LeadActivity).filter(
        LeadActivity.lead_id == lead_id
    ).order_by(LeadActivity.created_at.desc()).limit(limit).all()

# Tasks

def create_task(db: Session, data: TaskCreate) -> Task:
    # Convert Pydantic model to dict and handle special fields
    task_data = data.dict(exclude_unset=True)

    # Handle tags - convert list to JSON string if needed
    if 'tags' in task_data and isinstance(task_data['tags'], list):
        import json
        task_data['tags'] = json.dumps(task_data['tags'])

    # Remove any fields that shouldn't be set during creation
    task_data.pop('id', None)
    task_data.pop('created_at', None)
    task_data.pop('updated_at', None)
    task_data.pop('completed_at', None)

    # Create the task object
    obj = Task(**task_data)

    try:
        db.add(obj)
        db.commit()
        db.refresh(obj)

        # Create activity records for related entities
        if obj.contact_id:
            create_contact_activity(
                db, obj.contact_id, "task.created",
                f"Task '{obj.title}' was created"
            )
        if obj.lead_id:
            create_lead_activity(
                db, obj.lead_id, "task.created",
                f"Task '{obj.title}' was created"
            )

        # Relay.app integration
        send_relay_webhook(
            event="task.created",
            payload={
                "task_id": obj.id,
                "title": obj.title,
                "status": obj.status,
                "priority": obj.priority,
                "due_date": obj.due_date.isoformat() if obj.due_date else None,
                "created_at": obj.created_at.isoformat(),
            },
            db=db
        )

        return obj
    except Exception as e:
        db.rollback()
        raise e

def get_tasks(db: Session, status: Optional[str] = None, priority: Optional[str] = None,
              task_type: Optional[str] = None, contact_id: Optional[int] = None,
              lead_id: Optional[int] = None, account_id: Optional[int] = None) -> List[Task]:
    query = db.query(Task)

    if status:
        query = query.filter(Task.status == status)
    if priority:
        query = query.filter(Task.priority == priority)
    if task_type:
        query = query.filter(Task.task_type == task_type)
    if contact_id:
        query = query.filter(Task.contact_id == contact_id)
    if lead_id:
        query = query.filter(Task.lead_id == lead_id)
    if account_id:
        query = query.filter(Task.account_id == account_id)

    return query.order_by(Task.due_date.asc().nullslast(), Task.priority.desc()).all()

def get_task(db: Session, task_id: int) -> Optional[Task]:
    return db.query(Task).get(task_id)

def update_task(db: Session, task_id: int, data: TaskUpdate) -> Optional[Task]:
    task = db.query(Task).get(task_id)
    if not task:
        return None

    update_data = data.dict(exclude_unset=True)
    old_values = {}

    # Handle tags - convert list to JSON string if needed
    if 'tags' in update_data and isinstance(update_data['tags'], list):
        import json
        update_data['tags'] = json.dumps(update_data['tags'])

    for field, value in update_data.items():
        if hasattr(task, field):
            old_values[field] = getattr(task, field)
            setattr(task, field, value)

    # Handle completion status
    if update_data.get('completed') and not task.completed:
        task.completed_at = datetime.utcnow()
        task.status = "completed"
    elif update_data.get('completed') is False and task.completed:
        task.completed_at = None
        if task.status == "completed":
            task.status = "pending"

    task.updated_at = datetime.utcnow()

    try:
        db.commit()
        db.refresh(task)

        # Create activity records for related entities
        changes = [f"{k}: {old_values[k]} → {v}" for k, v in update_data.items()]
        activity_desc = f"Task '{task.title}' updated: {', '.join(changes)}"

        if task.contact_id:
            create_contact_activity(db, task.contact_id, "task.updated", activity_desc)
        if task.lead_id:
            create_lead_activity(db, task.lead_id, "task.updated", activity_desc)

        return task
    except Exception as e:
        db.rollback()
        raise e

def complete_task(db: Session, task_id: int) -> Optional[Task]:
    task = db.query(Task).get(task_id)
    if not task:
        return None

    task.completed = True
    task.completed_at = datetime.utcnow()
    task.status = "completed"
    task.updated_at = datetime.utcnow()

    try:
        db.commit()
        db.refresh(task)

        # Create activity records for related entities
        if task.contact_id:
            create_contact_activity(
                db, task.contact_id, "task.completed",
                f"Task '{task.title}' was completed"
            )
        if task.lead_id:
            create_lead_activity(
                db, task.lead_id, "task.completed",
                f"Task '{task.title}' was completed"
            )

        return task
    except Exception as e:
        db.rollback()
        raise e

def delete_task(db: Session, task_id: int) -> bool:
    task = db.query(Task).get(task_id)
    if not task:
        return False

    # Create activity records before deletion
    if task.contact_id:
        create_contact_activity(
            db, task.contact_id, "task.deleted",
            f"Task '{task.title}' was deleted"
        )
    if task.lead_id:
        create_lead_activity(
            db, task.lead_id, "task.deleted",
            f"Task '{task.title}' was deleted"
        )

    db.delete(task)
    db.commit()
    return True

def get_overdue_tasks(db: Session) -> List[Task]:
    """Get all overdue tasks that are not completed"""
    return db.query(Task).filter(
        Task.due_date < datetime.utcnow(),
        Task.completed == False,
        Task.status != "cancelled"
    ).order_by(Task.due_date.asc()).all()

def get_tasks_due_today(db: Session) -> List[Task]:
    """Get all tasks due today"""
    today = datetime.utcnow().date()
    return db.query(Task).filter(
        Task.due_date >= datetime.combine(today, datetime.min.time()),
        Task.due_date < datetime.combine(today, datetime.max.time()),
        Task.completed == False,
        Task.status != "cancelled"
    ).order_by(Task.due_date.asc()).all()

def get_task_summary(db: Session) -> Dict[str, Any]:
    """Get task summary statistics"""
    total_tasks = db.query(Task).count()
    completed_tasks = db.query(Task).filter(Task.completed == True).count()
    overdue_tasks = db.query(Task).filter(
        Task.due_date < datetime.utcnow(),
        Task.completed == False,
        Task.status != "cancelled"
    ).count()
    due_today = db.query(Task).filter(
        Task.due_date >= datetime.combine(datetime.utcnow().date(), datetime.min.time()),
        Task.due_date < datetime.combine(datetime.utcnow().date(), datetime.max.time()),
        Task.completed == False,
        Task.status != "cancelled"
    ).count()

    # Tasks by priority
    high_priority = db.query(Task).filter(
        Task.priority == "high",
        Task.completed == False,
        Task.status != "cancelled"
    ).count()

    urgent_priority = db.query(Task).filter(
        Task.priority == "urgent",
        Task.completed == False,
        Task.status != "cancelled"
    ).count()

    return {
        "total_tasks": total_tasks,
        "completed_tasks": completed_tasks,
        "pending_tasks": total_tasks - completed_tasks,
        "overdue_tasks": overdue_tasks,
        "due_today": due_today,
        "high_priority": high_priority,
        "urgent_priority": urgent_priority,
        "completion_rate": round((completed_tasks / total_tasks * 100) if total_tasks > 0 else 0, 1)
    }

# Webhooks

def create_webhook(db: Session, data: WebhookCreate) -> Webhook:
    webhook_data = data.dict()
    webhook_data["url"] = str(webhook_data["url"])
    obj = Webhook(**webhook_data)
    db.add(obj)
    db.commit()
    db.refresh(obj)
    return obj

def get_webhooks(db: Session) -> List[Webhook]:
    return db.query(Webhook).all()

def get_webhook(db: Session, webhook_id: int) -> Optional[Webhook]:
    return db.query(Webhook).get(webhook_id)

def update_webhook(db: Session, webhook_id: int, data: WebhookUpdate) -> Optional[Webhook]:
    webhook = db.query(Webhook).get(webhook_id)
    if not webhook:
        return None

    update_data = data.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(webhook, field, value)

    db.commit()
    db.refresh(webhook)
    return webhook

def delete_webhook(db: Session, webhook_id: int) -> bool:
    webhook = db.query(Webhook).get(webhook_id)
    if not webhook:
        return False
    db.delete(webhook)
    db.commit()
    return True

def create_webhook_log(db: Session, webhook_id: int, event_type: str, payload: dict,
                      response_status: Optional[int] = None, response_body: Optional[str] = None) -> WebhookLog:
    log = WebhookLog(
        webhook_id=webhook_id,
        event_type=event_type,
        payload=payload,
        response_status=response_status,
        response_body=response_body
    )
    db.add(log)
    db.commit()
    db.refresh(log)
    return log

def get_webhook_logs(db: Session, webhook_id: Optional[int] = None) -> List[WebhookLog]:
    query = db.query(WebhookLog)
    if webhook_id:
        query = query.filter(WebhookLog.webhook_id == webhook_id)
    return query.order_by(WebhookLog.created_at.desc()).all()

# User CRUD and authentication

def get_user_by_username(db: Session, username: str) -> Optional[User]:
    return db.query(User).filter(User.username == username).first()

def get_user_by_email(db: Session, email: str) -> Optional[User]:
    return db.query(User).filter(User.email == email).first()

def create_user(db: Session, username: str, email: str, password: str, role: str = "user") -> User:
    hashed_password = pwd_context.hash(password)
    user = User(username=username, email=email, password_hash=hashed_password, role=role)
    db.add(user)
    db.commit()
    db.refresh(user)
    return user

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def authenticate_user(db: Session, username: str, password: str) -> Optional[User]:
    user = get_user_by_username(db, username)
    if not user or not verify_password(password, user.password_hash):
        return None
    return user
