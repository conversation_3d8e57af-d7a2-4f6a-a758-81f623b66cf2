from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import create_account, get_accounts, get_account
from crm_project.schemas import AccountCreate, AccountRead

router = APIRouter(prefix="/accounts", tags=["Accounts"])

@router.post("/", response_model=AccountRead)
def add_account(payload: AccountCreate, db: Session = Depends(get_session)):
    return create_account(db, payload)

@router.get("/", response_model=list[AccountRead])
def list_accounts(db: Session = Depends(get_session)):
    return get_accounts(db)

@router.get("/{account_id}", response_model=AccountRead)
def get_one(account_id: int, db: Session = Depends(get_session)):
    account = get_account(db, account_id)
    if not account:
        raise HTTPException(status_code=404, detail="Account not found")
    return account
