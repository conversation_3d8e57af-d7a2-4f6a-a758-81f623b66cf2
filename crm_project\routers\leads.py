from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import (
    create_lead, get_leads, get_lead, update_lead, delete_lead,
    get_leads_by_stage, get_pipeline_summary,
    create_lead_note, get_lead_notes, delete_lead_note
)
from crm_project.schemas import (
    LeadCreate, LeadRead, LeadUpdate, LeadDetailRead,
    LeadNoteCreate, LeadNoteRead
)
from typing import Dict, Any

router = APIRouter(prefix="/leads", tags=["Leads"])

@router.post("/", response_model=LeadRead)
def add_lead(payload: LeadCreate, db: Session = Depends(get_session)):
    return create_lead(db, payload)

@router.get("/", response_model=list[LeadRead])
def list_leads(db: Session = Depends(get_session)):
    return get_leads(db)

@router.get("/{lead_id}", response_model=LeadDetailRead)
def get_lead_detail(lead_id: int, db: Session = Depends(get_session)):
    lead = get_lead(db, lead_id)
    if not lead:
        raise HTTPException(status_code=404, detail="Lead not found")

    # Add contact and account names
    result = LeadDetailRead.from_orm(lead)
    if lead.contact:
        result.contact_name = f"{lead.contact.first_name} {lead.contact.last_name}"
    if lead.account:
        result.account_name = lead.account.name

    return result

@router.put("/{lead_id}", response_model=LeadRead)
def update_lead_endpoint(lead_id: int, payload: LeadUpdate, db: Session = Depends(get_session)):
    lead = update_lead(db, lead_id, payload)
    if not lead:
        raise HTTPException(status_code=404, detail="Lead not found")
    return lead

@router.delete("/{lead_id}")
def delete_lead_endpoint(lead_id: int, db: Session = Depends(get_session)):
    if not delete_lead(db, lead_id):
        raise HTTPException(status_code=404, detail="Lead not found")
    return {"message": "Lead deleted successfully"}

@router.get("/stage/{stage}", response_model=list[LeadRead])
def get_leads_by_stage_endpoint(stage: str, db: Session = Depends(get_session)):
    return get_leads_by_stage(db, stage)

@router.get("/pipeline/summary")
def get_pipeline_summary_endpoint(db: Session = Depends(get_session)) -> Dict[str, Any]:
    return get_pipeline_summary(db)

# Lead Notes endpoints
@router.post("/{lead_id}/notes", response_model=LeadNoteRead)
def add_lead_note(lead_id: int, payload: LeadNoteCreate, db: Session = Depends(get_session)):
    # Verify lead exists
    lead = get_lead(db, lead_id)
    if not lead:
        raise HTTPException(status_code=404, detail="Lead not found")

    payload.lead_id = lead_id
    return create_lead_note(db, payload)

@router.get("/{lead_id}/notes", response_model=list[LeadNoteRead])
def list_lead_notes(lead_id: int, db: Session = Depends(get_session)):
    return get_lead_notes(db, lead_id)

@router.delete("/notes/{note_id}")
def delete_lead_note_endpoint(note_id: int, db: Session = Depends(get_session)):
    if not delete_lead_note(db, note_id):
        raise HTTPException(status_code=404, detail="Note not found")
    return {"message": "Note deleted successfully"}

@router.get("/{lead_id}", response_model=LeadRead)
def get_one(lead_id: int, db: Session = Depends(get_session)):
    lead = get_lead(db, lead_id)
    if not lead:
        raise HTTPException(status_code=404, detail="Lead not found")
    return lead
