from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session
from crm_project.database import get_session
from crm_project.crud import (
    create_contact, get_contacts, get_contact, update_contact,
    create_contact_note, get_contact_notes, delete_contact_note,
    get_contact_activities
)
from crm_project.schemas import (
    ContactCreate, ContactRead, ContactUpdate, ContactDetailRead,
    ContactNoteCreate, ContactNoteRead, ContactActivityRead
)
from crm_project.webhook_service import WebhookEvents, trigger_webhook_event
from typing import List

router = APIRouter(prefix="/contacts", tags=["Contacts"])

@router.post("/", response_model=ContactRead)
async def add_contact(
    payload: ContactCreate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session)
):
    try:
        contact = create_contact(db, payload)

        # Trigger webhook event
        contact_data = {
            "id": contact.id,
            "first_name": contact.first_name,
            "last_name": contact.last_name,
            "email": contact.email,
            "phone": contact.phone,
            "created_at": contact.created_at.isoformat()
        }
        background_tasks.add_task(trigger_webhook_event, WebhookEvents.CONTACT_CREATED, contact_data)

        return contact
    except IntegrityError:
        raise HTTPException(status_code=400, detail="Email already exists")

@router.get("/", response_model=list[ContactRead])
def list_contacts(db: Session = Depends(get_session)):
    return get_contacts(db)

@router.get("/{contact_id}", response_model=ContactDetailRead)
def get_one(contact_id: int, db: Session = Depends(get_session)):
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")

    # Add account name if available
    contact_dict = contact.__dict__.copy()
    contact_dict['account_name'] = contact.account.name if contact.account else None

    return contact

@router.put("/{contact_id}", response_model=ContactRead)
async def update_contact_endpoint(
    contact_id: int,
    payload: ContactUpdate,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session)
):
    contact = update_contact(db, contact_id, payload)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")

    # Trigger webhook event
    contact_data = {
        "id": contact.id,
        "first_name": contact.first_name,
        "last_name": contact.last_name,
        "email": contact.email,
        "phone": contact.phone,
        "job_title": contact.job_title,
        "department": contact.department,
        "updated_at": contact.updated_at.isoformat()
    }
    background_tasks.add_task(trigger_webhook_event, WebhookEvents.CONTACT_UPDATED, contact_data)

    return contact

@router.delete("/{contact_id}", response_model=bool)
async def delete_contact(
    contact_id: int,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session)
):
    # Get contact data before deletion for webhook
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")

    contact_data = {
        "id": contact.id,
        "first_name": contact.first_name,
        "last_name": contact.last_name,
        "email": contact.email,
        "phone": contact.phone
    }

    from crm_project.crud import delete_contact as crud_delete_contact
    success = crud_delete_contact(db, contact_id)

    if success:
        # Trigger webhook event
        background_tasks.add_task(trigger_webhook_event, WebhookEvents.CONTACT_DELETED, contact_data)

    return success

# Contact Notes endpoints
@router.post("/{contact_id}/notes", response_model=ContactNoteRead)
def add_contact_note(
    contact_id: int,
    content: str,
    note_type: str = "general",
    created_by: str = None,
    db: Session = Depends(get_session)
):
    # Verify contact exists
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")

    note_data = ContactNoteCreate(
        contact_id=contact_id,
        content=content,
        note_type=note_type,
        created_by=created_by
    )
    return create_contact_note(db, note_data)

@router.get("/{contact_id}/notes", response_model=List[ContactNoteRead])
def get_contact_notes_endpoint(contact_id: int, db: Session = Depends(get_session)):
    # Verify contact exists
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")

    return get_contact_notes(db, contact_id)

@router.delete("/notes/{note_id}", response_model=bool)
def delete_note(note_id: int, db: Session = Depends(get_session)):
    success = delete_contact_note(db, note_id)
    if not success:
        raise HTTPException(status_code=404, detail="Note not found")
    return success

# Contact Activities endpoint
@router.get("/{contact_id}/activities", response_model=List[ContactActivityRead])
def get_contact_activities_endpoint(
    contact_id: int,
    limit: int = 50,
    db: Session = Depends(get_session)
):
    # Verify contact exists
    contact = get_contact(db, contact_id)
    if not contact:
        raise HTTPException(status_code=404, detail="Contact not found")

    return get_contact_activities(db, contact_id, limit)
