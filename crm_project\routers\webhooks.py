from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List
from crm_project.database import get_session
from crm_project.crud import (
    create_webhook, get_webhooks, get_webhook, update_webhook, delete_webhook,
    get_webhook_logs
)
from crm_project.schemas import WebhookCreate, WebhookRead, WebhookUpdate, WebhookLogRead
from crm_project.webhook_service import Webhook<PERSON>vents, trigger_webhook_event
from crm_project.jwt_utils import get_current_username

router = APIRouter(prefix="/webhooks", tags=["Webhooks"])

@router.post("/", response_model=WebhookRead)
def create_webhook_endpoint(payload: WebhookCreate, db: Session = Depends(get_session), username: str = Depends(get_current_username)):
    """Create a new webhook (admin only)"""
    # You can add a role check here if needed
    return create_webhook(db, payload)

@router.get("/", response_model=List[WebhookRead])
def list_webhooks(db: Session = Depends(get_session)):
    """List all webhooks"""
    return get_webhooks(db)

@router.get("/{webhook_id}", response_model=WebhookRead)
def get_webhook_endpoint(webhook_id: int, db: Session = Depends(get_session)):
    """Get a specific webhook"""
    webhook = get_webhook(db, webhook_id)
    if not webhook:
        raise HTTPException(status_code=404, detail="Webhook not found")
    return webhook

@router.put("/{webhook_id}", response_model=WebhookRead)
def update_webhook_endpoint(
    webhook_id: int, 
    payload: WebhookUpdate, 
    db: Session = Depends(get_session)
):
    """Update a webhook"""
    webhook = update_webhook(db, webhook_id, payload)
    if not webhook:
        raise HTTPException(status_code=404, detail="Webhook not found")
    return webhook

@router.delete("/{webhook_id}", response_model=bool)
def delete_webhook_endpoint(webhook_id: int, db: Session = Depends(get_session)):
    """Delete a webhook"""
    success = delete_webhook(db, webhook_id)
    if not success:
        raise HTTPException(status_code=404, detail="Webhook not found")
    return success

@router.get("/{webhook_id}/logs", response_model=List[WebhookLogRead])
def get_webhook_logs_endpoint(webhook_id: int, db: Session = Depends(get_session)):
    """Get logs for a specific webhook"""
    webhook = get_webhook(db, webhook_id)
    if not webhook:
        raise HTTPException(status_code=404, detail="Webhook not found")
    return get_webhook_logs(db, webhook_id)

@router.get("/logs/all", response_model=List[WebhookLogRead])
def get_all_webhook_logs(db: Session = Depends(get_session)):
    """Get all webhook logs"""
    return get_webhook_logs(db)

@router.get("/events/available")
def get_available_events():
    """Get list of available webhook events"""
    return {
        "events": [
            {"name": WebhookEvents.CONTACT_CREATED, "description": "Triggered when a contact is created"},
            {"name": WebhookEvents.CONTACT_UPDATED, "description": "Triggered when a contact is updated"},
            {"name": WebhookEvents.CONTACT_DELETED, "description": "Triggered when a contact is deleted"},
            {"name": WebhookEvents.ACCOUNT_CREATED, "description": "Triggered when an account is created"},
            {"name": WebhookEvents.ACCOUNT_UPDATED, "description": "Triggered when an account is updated"},
            {"name": WebhookEvents.ACCOUNT_DELETED, "description": "Triggered when an account is deleted"},
            {"name": WebhookEvents.LEAD_CREATED, "description": "Triggered when a lead is created"},
            {"name": WebhookEvents.LEAD_UPDATED, "description": "Triggered when a lead is updated"},
            {"name": WebhookEvents.LEAD_DELETED, "description": "Triggered when a lead is deleted"},
            {"name": WebhookEvents.TASK_CREATED, "description": "Triggered when a task is created"},
            {"name": WebhookEvents.TASK_UPDATED, "description": "Triggered when a task is updated"},
            {"name": WebhookEvents.TASK_COMPLETED, "description": "Triggered when a task is completed"},
            {"name": WebhookEvents.TASK_DELETED, "description": "Triggered when a task is deleted"},
        ]
    }

@router.post("/test/{webhook_id}")
async def test_webhook(
    webhook_id: int, 
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_session)
):
    """Test a webhook by sending a test event"""
    webhook = get_webhook(db, webhook_id)
    if not webhook:
        raise HTTPException(status_code=404, detail="Webhook not found")
    
    test_data = {
        "test": True,
        "webhook_id": webhook_id,
        "message": "This is a test webhook event"
    }
    
    # Trigger webhook in background
    background_tasks.add_task(trigger_webhook_event, "webhook.test", test_data)
    
    return {"message": "Test webhook triggered", "webhook_id": webhook_id}
