import asyncio
import httpx
import hashlib
import hmac
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from crm_project.models import Webhook
from crm_project.crud import get_webhooks, create_webhook_log, update_webhook
from crm_project.database import SessionLocal

class WebhookService:
    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def trigger_webhooks(self, event_type: str, data: Dict[str, Any]):
        """Trigger all active webhooks for a specific event type"""
        db = SessionLocal()
        try:
            webhooks = get_webhooks(db)
            active_webhooks = [w for w in webhooks if w.active and event_type in w.events]
            
            if not active_webhooks:
                return
            
            # Create the webhook payload
            payload = {
                "event_type": event_type,
                "data": data,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # Trigger webhooks concurrently
            tasks = []
            for webhook in active_webhooks:
                task = asyncio.create_task(self._send_webhook(db, webhook, payload))
                tasks.append(task)
            
            await asyncio.gather(*tasks, return_exceptions=True)
            
        finally:
            db.close()
    
    async def _send_webhook(self, db: Session, webhook: Webhook, payload: Dict[str, Any]):
        """Send a single webhook request"""
        try:
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "PydanticAI-CRM-Webhook/1.0"
            }
            
            # Add signature if secret is provided
            if webhook.secret:
                signature = self._generate_signature(webhook.secret, payload)
                headers["X-Webhook-Signature"] = signature
            
            response = await self.client.post(
                str(webhook.url),
                json=payload,
                headers=headers
            )
            
            # Log the webhook attempt
            create_webhook_log(
                db=db,
                webhook_id=webhook.id,
                event_type=payload["event_type"],
                payload=payload,
                response_status=response.status_code,
                response_body=response.text[:1000]  # Limit response body size
            )
            
            # Update last triggered time
            webhook.last_triggered = datetime.utcnow()
            db.commit()
            
            return response.status_code
            
        except Exception as e:
            # Log failed webhook attempt
            create_webhook_log(
                db=db,
                webhook_id=webhook.id,
                event_type=payload["event_type"],
                payload=payload,
                response_status=None,
                response_body=str(e)[:1000]
            )
            return None
    
    def _generate_signature(self, secret: str, payload: Dict[str, Any]) -> str:
        """Generate HMAC signature for webhook verification"""
        payload_bytes = json.dumps(payload, sort_keys=True).encode('utf-8')
        signature = hmac.new(
            secret.encode('utf-8'),
            payload_bytes,
            hashlib.sha256
        ).hexdigest()
        return f"sha256={signature}"
    
    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

# Global webhook service instance
webhook_service = WebhookService()

# Event types
class WebhookEvents:
    CONTACT_CREATED = "contact.created"
    CONTACT_UPDATED = "contact.updated"
    CONTACT_DELETED = "contact.deleted"
    ACCOUNT_CREATED = "account.created"
    ACCOUNT_UPDATED = "account.updated"
    ACCOUNT_DELETED = "account.deleted"
    LEAD_CREATED = "lead.created"
    LEAD_UPDATED = "lead.updated"
    LEAD_DELETED = "lead.deleted"
    TASK_CREATED = "task.created"
    TASK_UPDATED = "task.updated"
    TASK_COMPLETED = "task.completed"
    TASK_DELETED = "task.deleted"

# Helper function to trigger webhooks
async def trigger_webhook_event(event_type: str, data: Dict[str, Any]):
    """Helper function to trigger webhook events"""
    await webhook_service.trigger_webhooks(event_type, data)
