<!DOCTYPE html>
<html>
<head>
    <title>Enhanced Contact Management - CRM</title>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background-color: #f8f9fa; 
        }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            margin-bottom: 20px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
        }
        .nav { margin-bottom: 20px; }
        .nav a { 
            margin-right: 15px; 
            color: #007bff; 
            text-decoration: none; 
            font-weight: 500;
        }
        .nav a:hover { text-decoration: underline; }
        
        .main-content { display: flex; gap: 20px; }
        .contacts-list { flex: 1; }
        .contact-detail { flex: 1; }
        
        .card { 
            background: white; 
            border-radius: 8px; 
            padding: 20px; 
            box-shadow: 0 2px 4px rgba(0,0,0,0.1); 
            margin-bottom: 20px;
        }
        
        .contact-item { 
            padding: 15px; 
            border: 1px solid #e9ecef; 
            border-radius: 6px; 
            margin-bottom: 10px; 
            cursor: pointer; 
            transition: all 0.2s;
        }
        .contact-item:hover { background-color: #f8f9fa; border-color: #007bff; }
        .contact-item.selected { background-color: #e3f2fd; border-color: #007bff; }
        
        .contact-name { font-weight: 600; font-size: 16px; color: #333; }
        .contact-email { color: #666; font-size: 14px; }
        .contact-meta { color: #999; font-size: 12px; margin-top: 5px; }
        
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: 500; }
        .form-group input, .form-group select, .form-group textarea { 
            width: 100%; 
            padding: 8px 12px; 
            border: 1px solid #ddd; 
            border-radius: 4px; 
            font-size: 14px;
        }
        .form-group textarea { resize: vertical; min-height: 80px; }
        
        .btn { 
            padding: 8px 16px; 
            border: none; 
            border-radius: 4px; 
            cursor: pointer; 
            font-size: 14px; 
            font-weight: 500;
            margin-right: 8px;
            margin-bottom: 8px;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-primary:hover { background-color: #0056b3; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-success:hover { background-color: #218838; }
        .btn-danger { background-color: #dc3545; color: white; }
        .btn-danger:hover { background-color: #c82333; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-secondary:hover { background-color: #545b62; }
        
        .tags { margin: 10px 0; }
        .tag { 
            display: inline-block; 
            background-color: #e9ecef; 
            color: #495057; 
            padding: 4px 8px; 
            border-radius: 12px; 
            font-size: 12px; 
            margin-right: 5px; 
            margin-bottom: 5px;
        }
        
        .notes-section, .activities-section { margin-top: 20px; }
        .note-item, .activity-item { 
            background-color: #f8f9fa; 
            padding: 12px; 
            border-radius: 6px; 
            margin-bottom: 10px; 
            border-left: 4px solid #007bff;
        }
        .note-header, .activity-header { 
            font-size: 12px; 
            color: #666; 
            margin-bottom: 5px; 
        }
        .note-content { font-size: 14px; line-height: 1.4; }
        
        .hidden { display: none; }
        .loading { text-align: center; padding: 20px; color: #666; }
        
        .search-box { 
            width: 100%; 
            padding: 10px; 
            border: 1px solid #ddd; 
            border-radius: 6px; 
            margin-bottom: 15px; 
            font-size: 14px;
        }
        
        .status-badge { 
            display: inline-block; 
            padding: 2px 8px; 
            border-radius: 12px; 
            font-size: 11px; 
            font-weight: 500; 
            text-transform: uppercase;
        }
        .status-active { background-color: #d4edda; color: #155724; }
        .status-prospect { background-color: #fff3cd; color: #856404; }
        .status-customer { background-color: #d1ecf1; color: #0c5460; }
        .status-inactive { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="nav">
                <a href="/">← Back to Home</a>
                <a href="/app">Simple CRM</a>
                <a href="/webhooks-ui">Webhooks</a>
                <a href="/docs">API Docs</a>
            </div>
            <h1>Enhanced Contact Management</h1>
            <p>Manage contacts with notes, activities, and detailed information</p>
        </div>
        
        <div class="main-content">
            <!-- Contacts List -->
            <div class="contacts-list">
                <div class="card">
                    <h3>Contacts</h3>
                    <input type="text" class="search-box" id="searchBox" placeholder="Search contacts...">
                    <button class="btn btn-primary" onclick="showCreateForm()">+ Add New Contact</button>
                    <div id="contactsList" class="loading">Loading contacts...</div>
                </div>
            </div>
            
            <!-- Contact Detail -->
            <div class="contact-detail">
                <div class="card" id="contactDetail">
                    <div id="noContactSelected">
                        <h3>Select a Contact</h3>
                        <p>Choose a contact from the list to view and edit details, add notes, and see activity history.</p>
                    </div>
                    
                    <div id="contactInfo" class="hidden">
                        <div id="contactHeader"></div>
                        <div id="contactForm"></div>
                        <div id="notesSection" class="notes-section"></div>
                        <div id="activitiesSection" class="activities-section"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Create Contact Modal -->
    <div id="createModal" class="hidden" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 8px; width: 500px; max-width: 90vw;">
            <h3>Create New Contact</h3>
            <form id="createContactForm">
                <div class="form-group">
                    <label>First Name *</label>
                    <input type="text" name="first_name" required>
                </div>
                <div class="form-group">
                    <label>Last Name *</label>
                    <input type="text" name="last_name" required>
                </div>
                <div class="form-group">
                    <label>Email *</label>
                    <input type="email" name="email" required>
                </div>
                <div class="form-group">
                    <label>Phone</label>
                    <input type="text" name="phone">
                </div>
                <div class="form-group">
                    <label>Job Title</label>
                    <input type="text" name="job_title">
                </div>
                <div class="form-group">
                    <label>Department</label>
                    <input type="text" name="department">
                </div>
                <div class="form-group">
                    <label>Status</label>
                    <select name="status">
                        <option value="active">Active</option>
                        <option value="prospect">Prospect</option>
                        <option value="customer">Customer</option>
                        <option value="inactive">Inactive</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Lead Source</label>
                    <input type="text" name="lead_source" placeholder="e.g., Website, Referral, Cold Call">
                </div>
                <div style="text-align: right; margin-top: 20px;">
                    <button type="button" class="btn btn-secondary" onclick="hideCreateForm()">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Contact</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let contacts = [];
        let selectedContact = null;
        
        // Load contacts on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadContacts();
            
            // Search functionality
            document.getElementById('searchBox').addEventListener('input', function(e) {
                filterContacts(e.target.value);
            });
        });
        
        async function loadContacts() {
            try {
                const response = await fetch('/contacts/');
                contacts = await response.json();
                displayContacts(contacts);
            } catch (error) {
                console.error('Error loading contacts:', error);
                document.getElementById('contactsList').innerHTML = '<p>Error loading contacts</p>';
            }
        }
        
        function displayContacts(contactsToShow) {
            const container = document.getElementById('contactsList');
            
            if (contactsToShow.length === 0) {
                container.innerHTML = '<p>No contacts found</p>';
                return;
            }
            
            container.innerHTML = contactsToShow.map(contact => `
                <div class="contact-item" onclick="selectContact(${contact.id})">
                    <div class="contact-name">${contact.first_name} ${contact.last_name}</div>
                    <div class="contact-email">${contact.email}</div>
                    <div class="contact-meta">
                        <span class="status-badge status-${contact.status}">${contact.status}</span>
                        ${contact.job_title ? `• ${contact.job_title}` : ''}
                        ${contact.department ? ` (${contact.department})` : ''}
                    </div>
                </div>
            `).join('');
        }
        
        function filterContacts(searchTerm) {
            const filtered = contacts.filter(contact => 
                contact.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                contact.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                contact.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (contact.job_title && contact.job_title.toLowerCase().includes(searchTerm.toLowerCase())) ||
                (contact.department && contact.department.toLowerCase().includes(searchTerm.toLowerCase()))
            );
            displayContacts(filtered);
        }
        
        async function selectContact(contactId) {
            try {
                // Update UI to show selected
                document.querySelectorAll('.contact-item').forEach(item => item.classList.remove('selected'));
                event.target.closest('.contact-item').classList.add('selected');
                
                // Load detailed contact info
                const response = await fetch(`/contacts/${contactId}`);
                selectedContact = await response.json();
                
                displayContactDetail(selectedContact);
                loadContactNotes(contactId);
                loadContactActivities(contactId);
                
            } catch (error) {
                console.error('Error loading contact details:', error);
            }
        }
        
        function displayContactDetail(contact) {
            document.getElementById('noContactSelected').classList.add('hidden');
            document.getElementById('contactInfo').classList.remove('hidden');
            
            // Contact header
            document.getElementById('contactHeader').innerHTML = `
                <h3>${contact.first_name} ${contact.last_name}</h3>
                <p>${contact.email} ${contact.phone ? `• ${contact.phone}` : ''}</p>
                <div class="tags">
                    <span class="status-badge status-${contact.status}">${contact.status}</span>
                    ${contact.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                </div>
            `;
            
            // Contact form for editing
            document.getElementById('contactForm').innerHTML = `
                <h4>Contact Information</h4>
                <form id="updateContactForm">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div class="form-group">
                            <label>First Name</label>
                            <input type="text" name="first_name" value="${contact.first_name}">
                        </div>
                        <div class="form-group">
                            <label>Last Name</label>
                            <input type="text" name="last_name" value="${contact.last_name}">
                        </div>
                        <div class="form-group">
                            <label>Email</label>
                            <input type="email" name="email" value="${contact.email}">
                        </div>
                        <div class="form-group">
                            <label>Phone</label>
                            <input type="text" name="phone" value="${contact.phone || ''}">
                        </div>
                        <div class="form-group">
                            <label>Job Title</label>
                            <input type="text" name="job_title" value="${contact.job_title || ''}">
                        </div>
                        <div class="form-group">
                            <label>Department</label>
                            <input type="text" name="department" value="${contact.department || ''}">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Address</label>
                        <textarea name="address">${contact.address || ''}</textarea>
                    </div>
                    <button type="submit" class="btn btn-primary">Update Contact</button>
                    <button type="button" class="btn btn-danger" onclick="deleteContact(${contact.id})">Delete Contact</button>
                </form>
            `;
            
            // Add form submit handler
            document.getElementById('updateContactForm').onsubmit = function(e) {
                e.preventDefault();
                updateContact(contact.id, new FormData(e.target));
            };
        }

        async function updateContact(contactId, formData) {
            try {
                const data = Object.fromEntries(formData);
                const response = await fetch(`/contacts/${contactId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    alert('Contact updated successfully!');
                    loadContacts(); // Refresh the list
                    selectContact(contactId); // Refresh the detail view
                } else {
                    alert('Error updating contact');
                }
            } catch (error) {
                console.error('Error updating contact:', error);
                alert('Error updating contact');
            }
        }

        async function deleteContact(contactId) {
            if (!confirm('Are you sure you want to delete this contact?')) return;

            try {
                const response = await fetch(`/contacts/${contactId}`, { method: 'DELETE' });
                if (response.ok) {
                    alert('Contact deleted successfully!');
                    loadContacts();
                    document.getElementById('noContactSelected').classList.remove('hidden');
                    document.getElementById('contactInfo').classList.add('hidden');
                } else {
                    alert('Error deleting contact');
                }
            } catch (error) {
                console.error('Error deleting contact:', error);
                alert('Error deleting contact');
            }
        }

        async function loadContactNotes(contactId) {
            try {
                const response = await fetch(`/contacts/${contactId}/notes`);
                const notes = await response.json();

                document.getElementById('notesSection').innerHTML = `
                    <h4>Notes</h4>
                    <div class="form-group">
                        <textarea id="newNoteContent" placeholder="Add a note..."></textarea>
                        <select id="noteType">
                            <option value="general">General</option>
                            <option value="call">Call</option>
                            <option value="meeting">Meeting</option>
                            <option value="email">Email</option>
                        </select>
                        <button class="btn btn-success" onclick="addNote(${contactId})">Add Note</button>
                    </div>
                    <div id="notesList">
                        ${notes.map(note => `
                            <div class="note-item">
                                <div class="note-header">
                                    ${note.note_type.toUpperCase()} • ${new Date(note.created_at).toLocaleString()}
                                    ${note.created_by ? ` • by ${note.created_by}` : ''}
                                    <button class="btn btn-danger" style="float: right; padding: 2px 6px; font-size: 11px;" onclick="deleteNote(${note.id})">Delete</button>
                                </div>
                                <div class="note-content">${note.content}</div>
                            </div>
                        `).join('')}
                    </div>
                `;
            } catch (error) {
                console.error('Error loading notes:', error);
            }
        }

        async function addNote(contactId) {
            const content = document.getElementById('newNoteContent').value.trim();
            const noteType = document.getElementById('noteType').value;

            if (!content) {
                alert('Please enter note content');
                return;
            }

            try {
                const response = await fetch(`/contacts/${contactId}/notes?content=${encodeURIComponent(content)}&note_type=${noteType}`, {
                    method: 'POST'
                });

                if (response.ok) {
                    document.getElementById('newNoteContent').value = '';
                    loadContactNotes(contactId);
                    loadContactActivities(contactId); // Refresh activities
                } else {
                    alert('Error adding note');
                }
            } catch (error) {
                console.error('Error adding note:', error);
                alert('Error adding note');
            }
        }

        async function deleteNote(noteId) {
            if (!confirm('Are you sure you want to delete this note?')) return;

            try {
                const response = await fetch(`/contacts/notes/${noteId}`, { method: 'DELETE' });
                if (response.ok) {
                    loadContactNotes(selectedContact.id);
                    loadContactActivities(selectedContact.id);
                } else {
                    alert('Error deleting note');
                }
            } catch (error) {
                console.error('Error deleting note:', error);
                alert('Error deleting note');
            }
        }

        async function loadContactActivities(contactId) {
            try {
                const response = await fetch(`/contacts/${contactId}/activities`);
                const activities = await response.json();

                document.getElementById('activitiesSection').innerHTML = `
                    <h4>Recent Activity</h4>
                    <div id="activitiesList">
                        ${activities.map(activity => `
                            <div class="activity-item">
                                <div class="activity-header">
                                    ${activity.activity_type.replace('.', ' ').toUpperCase()} • ${new Date(activity.created_at).toLocaleString()}
                                </div>
                                <div class="note-content">${activity.description}</div>
                            </div>
                        `).join('')}
                    </div>
                `;
            } catch (error) {
                console.error('Error loading activities:', error);
            }
        }

        function showCreateForm() {
            document.getElementById('createModal').classList.remove('hidden');
        }

        function hideCreateForm() {
            document.getElementById('createModal').classList.add('hidden');
            document.getElementById('createContactForm').reset();
        }

        // Create contact form handler
        document.getElementById('createContactForm').onsubmit = async function(e) {
            e.preventDefault();

            try {
                const formData = new FormData(e.target);
                const data = Object.fromEntries(formData);

                const response = await fetch('/contacts/', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    hideCreateForm();
                    loadContacts();
                    alert('Contact created successfully!');
                } else {
                    const error = await response.json();
                    alert('Error creating contact: ' + (error.detail || 'Unknown error'));
                }
            } catch (error) {
                console.error('Error creating contact:', error);
                alert('Error creating contact');
            }
        };
    </script>
</body>
</html>
